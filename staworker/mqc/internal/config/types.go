package config

import (
	corev1 "k8s.io/api/core/v1"
)

type (
	TaskInfo struct {
		TypeName  string // 任务类型名称
		TaskID    string `json:",optional"` // 任务ID
		ExecuteID string `json:",optional"` // 压测用例执行ID
	}

	JobInfo struct {
		ClusterName string
		Namespace   string `json:",optional"`
		Image       string `json:",optional"`
		PvcName     string
		MountPath   string
		Resources   corev1.ResourceRequirements
	}
)
